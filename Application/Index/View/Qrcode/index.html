<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="icon" href="__PUBLIC__/static/annie/img/fovicon.ico">
    <title>二维码生成 - {$con.webname}</title>
    <meta name="description" content="在线二维码生成器，支持文本、网址等内容生成二维码">
    <meta name="keywords" content="二维码生成器,QR码,在线工具">
    <link href="__PUBLIC__/static/annie/css/annie.css" rel="stylesheet">
    <link href="__PUBLIC__/static/annie/css/app.css" rel="stylesheet">
    <link href="__PUBLIC__/static/annie/css/annieMod.css" rel="stylesheet">
    <style>
        .qrcode-container {
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .input-group {
            margin-bottom: 20px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        
        .input-group textarea {
            width: 100%;
            min-height: 100px;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            resize: vertical;
            box-sizing: border-box;
        }
        
        .input-group textarea:focus {
            outline: none;
            border-color: #4CAF50;
        }
        
        .btn-generate {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            width: 100%;
        }
        
        .btn-generate:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        }
        
        .qrcode-result {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 10px;
            display: none;
        }
        
        #qrcode {
            margin: 20px auto;
            display: inline-block;
        }
        
        .download-btn {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-top: 15px;
        }
        
        .download-btn:hover {
            background: #1976D2;
        }
        
        .back-btn {
            display: inline-block;
            margin-bottom: 20px;
            color: #666;
            text-decoration: none;
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 6px;
            transition: all 0.3s;
        }
        
        .back-btn:hover {
            background: #f5f5f5;
            text-decoration: none;
        }
        
        @media (max-width: 768px) {
            .qrcode-container {
                margin: 10px;
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="YF28">
        <div class="YF28-header">
            <div class="YF28-header-main">
                <div class="text">
                    <p>二维码生成器</p>
                    <p>在线生成二维码，支持文本、网址等内容</p>
                </div>
            </div>
            <div class="YF28-nav">
                <a href="/">返回首页</a>
            </div>
        </div>
        
        <div class="qrcode-container">
            <a href="/Utils" class="back-btn">← 返回工具库</a>
            
            <div class="input-group">
                <label for="qrtext">请输入要生成二维码的内容：</label>
                <textarea id="qrtext" placeholder="请输入文本、网址或其他内容..."></textarea>
            </div>
            
            <button class="btn-generate" onclick="generateQRCode()">生成二维码</button>
            
            <div class="qrcode-result" id="result">
                <h3>生成的二维码：</h3>
                <div id="qrcode"></div>
                <button class="download-btn" onclick="downloadQRCode()">下载二维码</button>
            </div>
        </div>
    </div>
    
    <script src="__PUBLIC__/js/jquery-1.9.1.min.js"></script>
    <script src="__PUBLIC__/js/qrcode.js"></script>
    <script>
        function generateQRCode() {
            var text = document.getElementById('qrtext').value.trim();
            
            if (!text) {
                alert('请输入要生成二维码的内容！');
                return;
            }
            
            // 清空之前的二维码
            document.getElementById('qrcode').innerHTML = '';
            
            try {
                // 创建二维码
                var qr = qrcode(0, 'M');
                qr.addData(text);
                qr.make();
                
                // 生成二维码图片
                var qrCodeDiv = document.getElementById('qrcode');
                qrCodeDiv.innerHTML = qr.createImgTag(4, 8);
                
                // 显示结果区域
                document.getElementById('result').style.display = 'block';
                
                // 滚动到结果区域
                document.getElementById('result').scrollIntoView({ behavior: 'smooth' });
                
            } catch (e) {
                alert('生成二维码失败，请检查输入内容！');
                console.error(e);
            }
        }
        
        function downloadQRCode() {
            var img = document.querySelector('#qrcode img');
            if (!img) {
                alert('请先生成二维码！');
                return;
            }
            
            // 创建下载链接
            var link = document.createElement('a');
            link.download = 'qrcode.png';
            link.href = img.src;
            link.click();
        }
        
        // 回车键生成二维码
        document.getElementById('qrtext').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && e.ctrlKey) {
                generateQRCode();
            }
        });
    </script>
</body>
</html>
